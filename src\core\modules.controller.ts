import { Controller, Get, Post, Body, Param } from '@nestjs/common';
import { HubService } from './hub.service';

/**
 * Contrôleur pour tester les nouveaux modules autonomes
 */
@Controller('modules')
export class ModulesController {
    constructor(private readonly hubService: HubService) {}

    /**
     * Obtenir l'état de tous les modules
     */
    @Get('status')
    getModulesStatus() {
        return {
            success: true,
            data: this.hubService.getModulesStatus(),
        };
    }

    /**
     * Obtenir l'état des liens
     */
    @Get('links')
    getLinksStatus() {
        return {
            success: true,
            data: this.hubService.getStatus().links,
        };
    }

    /**
     * Activer/désactiver un lien
     */
    @Post('links/:linkName/toggle')
    async toggleLink(@Param('linkName') linkName: string, @Body('enabled') enabled: boolean) {
        try {
            await this.hubService.toggleLink(linkName, enabled);
            return {
                success: true,
                message: `Link '${linkName}' ${enabled ? 'enabled' : 'disabled'}`,
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
            };
        }
    }
}
