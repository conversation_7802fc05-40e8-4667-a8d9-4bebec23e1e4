/**
 * Module OBS autonome
 * Gère sa propre connexion WebSocket, ses états et sa logique
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { OBSWebSocket } from 'obs-websocket-js';
import { BaseModule } from '../core/base/base-module';
import { IMicrophoneModule, MODULE_EVENTS } from '../core/interfaces/module.interface';
import { obsConfig, modulesConfig } from '../config';
import { OBSVolumeAdjustment, OBSAudioSource, OBSScene, OBSSceneState, OBSSceneItem } from './obs.types';

@Injectable()
export class OBSModuleService extends BaseModule implements IMicrophoneModule, OnModuleInit, OnModuleDestroy {
    private obs: OBSWebSocket;
    private reconnectTimer?: NodeJS.Timeout;
    private pingTimer?: NodeJS.Timeout;
    private isConnecting = false;
    private readonly config = obsConfig;

    // États internes du module
    private audioSources: Record<string, OBSAudioSource> = {};
    private scenes: Record<string, OBSSceneState> = {};
    private currentScene: string | null = null;

    constructor() {
        super('obs', modulesConfig.obs.enabled);
        this.obs = new OBSWebSocket();
        this.setupEventListeners();
        this.log('OBS module created', { enabled: this.enabled });
    }

    async onModuleInit() {
        if (this.enabled) {
            await this.start();
        } else {
            this.log('Module disabled - not starting');
        }
    }

    async onModuleDestroy() {
        await this.stop();
    }

    async start(): Promise<void> {
        if (!this.enabled) {
            this.log('Cannot start - module is disabled');
            return;
        }

        if (this.config.autoConnect) {
            await this.connect();
        }
    }

    async stop(): Promise<void> {
        this.log('Stopping OBS module...');

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = undefined;
        }

        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = undefined;
        }

        if (this.obs.identified) {
            await this.obs.disconnect();
        }

        this.updateConnectionStatus({ connected: false });
        this.log('OBS module stopped');
    }

    // === INTERFACE IMicrophoneModule ===

    getMicState(sourceName: string): boolean | undefined {
        const source = this.audioSources[sourceName];
        return source ? !source.inputMuted : undefined; // Inverser : muted = false signifie actif
    }

    getAllMicStates(): Record<string, boolean> {
        const states: Record<string, boolean> = {};
        for (const [name, source] of Object.entries(this.audioSources)) {
            states[name] = !source.inputMuted; // Inverser : muted = false signifie actif
        }
        return states;
    }

    async setMicState(sourceName: string, active: boolean): Promise<void> {
        if (!this.isReady()) {
            throw new Error('OBS module not ready');
        }

        try {
            // Dans OBS, actif = non muted
            await this.setSourceMute(sourceName, !active);
        } catch (error) {
            this.handleError(error, `Failed to set mic state: ${sourceName}`);
            throw error;
        }
    }

    // === MÉTHODES PUBLIQUES - OBSERVATION DES ÉTATS ===

    /**
     * Obtenir la scène actuelle
     */
    getCurrentScene(): string | null {
        return this.currentScene;
    }

    /**
     * Obtenir toutes les scènes et leurs états
     */
    getAllScenes(): Record<string, OBSSceneState> {
        return { ...this.scenes };
    }

    /**
     * Obtenir l'état d'une scène spécifique
     */
    getSceneState(sceneName: string): OBSSceneState | undefined {
        return this.scenes[sceneName];
    }

    /**
     * Vérifier si une source est visible dans la scène actuelle
     */
    isSourceVisibleInCurrentScene(sourceName: string): boolean | undefined {
        if (!this.currentScene) return undefined;

        const currentSceneState = this.scenes[this.currentScene];
        if (!currentSceneState) return undefined;

        const source = currentSceneState.sources[sourceName];
        return source ? source.sceneItemVisible && source.sceneItemEnabled : undefined;
    }

    /**
     * Obtenir toutes les sources visibles dans la scène actuelle
     */
    getVisibleSourcesInCurrentScene(): string[] {
        if (!this.currentScene) return [];

        const currentSceneState = this.scenes[this.currentScene];
        if (!currentSceneState) return [];

        return Object.entries(currentSceneState.sources)
            .filter(([_, source]) => source.sceneItemVisible && source.sceneItemEnabled)
            .map(([sourceName, _]) => sourceName);
    }

    /**
     * Obtenir les niveaux de volume de toutes les sources audio
     */
    getAllAudioLevels(): Record<string, { db: number; mul: number; muted: boolean }> {
        const levels: Record<string, { db: number; mul: number; muted: boolean }> = {};

        for (const [name, source] of Object.entries(this.audioSources)) {
            levels[name] = {
                db: source.inputVolumeDb,
                mul: source.inputVolumeMul,
                muted: source.inputMuted,
            };
        }

        return levels;
    }

    // === MÉTHODES PUBLIQUES OBS ===

    /**
     * Ajuster le volume d'une source audio
     */
    async setSourceVolume(adjustment: OBSVolumeAdjustment): Promise<void> {
        if (!this.isReady()) {
            throw new Error('OBS not connected');
        }

        try {
            const params: any = { inputName: adjustment.sourceName };

            if (adjustment.volumeDb !== undefined) {
                params.inputVolumeDb = adjustment.volumeDb;
            } else if (adjustment.volumeMul !== undefined) {
                params.inputVolumeMul = adjustment.volumeMul;
            } else if (adjustment.volumePercent !== undefined) {
                // Convertir pourcentage en multiplier (0-100% -> 0.0-1.0)
                params.inputVolumeMul = adjustment.volumePercent / 100;
            }

            await this.obs.call('SetInputVolume', params);
            this.log(`Volume set for "${adjustment.sourceName}"`);
        } catch (error) {
            this.handleError(error, `Failed to set volume for "${adjustment.sourceName}"`);
            throw error;
        }
    }

    /**
     * Ajuster le volume par pourcentage relatif
     */
    async adjustSourceVolumePercent(sourceName: string, percentAdjustment: number): Promise<void> {
        if (!this.isReady()) {
            throw new Error('OBS not connected');
        }

        try {
            // Obtenir le volume actuel
            const currentInfo = await this.obs.call('GetInputVolume', { inputName: sourceName });
            const currentMul = currentInfo.inputVolumeMul;

            // Appliquer l'ajustement en pourcentage
            const adjustmentMul = percentAdjustment / 100;
            const newMul = Math.max(0, Math.min(1, currentMul + adjustmentMul));

            await this.obs.call('SetInputVolume', {
                inputName: sourceName,
                inputVolumeMul: newMul,
            });

            this.log(`Volume adjusted for "${sourceName}": ${percentAdjustment}% (${currentMul} -> ${newMul})`);
        } catch (error) {
            this.handleError(error, `Failed to adjust volume for "${sourceName}"`);
            throw error;
        }
    }

    /**
     * Activer/désactiver le mute d'une source
     */
    async setSourceMute(sourceName: string, muted: boolean): Promise<void> {
        if (!this.isReady()) {
            throw new Error('OBS not connected');
        }

        try {
            await this.obs.call('SetInputMute', {
                inputName: sourceName,
                inputMuted: muted,
            });

            this.log(`${muted ? 'Muted' : 'Unmuted'} source "${sourceName}"`);
        } catch (error) {
            this.handleError(error, `Failed to set mute for "${sourceName}"`);
            throw error;
        }
    }

    /**
     * Basculer le mute d'une source
     */
    async toggleSourceMute(sourceName: string): Promise<boolean> {
        if (!this.isReady()) {
            throw new Error('OBS not connected');
        }

        try {
            const result = await this.obs.call('ToggleInputMute', { inputName: sourceName });
            this.log(`Toggled mute for "${sourceName}" - now ${result.inputMuted ? 'muted' : 'unmuted'}`);
            return result.inputMuted;
        } catch (error) {
            this.handleError(error, `Failed to toggle mute for "${sourceName}"`);
            throw error;
        }
    }

    /**
     * Obtenir les informations d'une source audio
     */
    async getSourceInfo(sourceName: string): Promise<OBSAudioSource> {
        if (!this.isReady()) {
            throw new Error('OBS not connected');
        }

        try {
            const volumeInfo = await this.obs.call('GetInputVolume', { inputName: sourceName });
            const muteInfo = await this.obs.call('GetInputMute', { inputName: sourceName });

            return {
                inputName: sourceName,
                inputVolumeMul: volumeInfo.inputVolumeMul,
                inputVolumeDb: volumeInfo.inputVolumeDb,
                inputMuted: muteInfo.inputMuted,
            };
        } catch (error) {
            this.handleError(error, `Failed to get info for "${sourceName}"`);
            throw error;
        }
    }

    // === MÉTHODES PRIVÉES ===

    private async connect(): Promise<void> {
        if (this.isConnecting || this.obs.identified) {
            return;
        }

        this.isConnecting = true;

        try {
            this.log(`Connecting to OBS at ${this.config.network.host}:${this.config.network.port}...`);

            const url = `ws://${this.config.network.host}:${this.config.network.port}`;
            const connectionInfo = await this.obs.connect(url, this.config.network.password);

            this.log(`Connected to OBS WebSocket v${connectionInfo.obsWebSocketVersion} (RPC v${connectionInfo.negotiatedRpcVersion})`);

            this.updateConnectionStatus({ connected: true });
            this.startPing();

            // Initialiser l'état des scènes
            await this.initializeSceneStates();
        } catch (error) {
            this.handleError(error, 'Failed to connect to OBS');
            this.scheduleReconnect();
        } finally {
            this.isConnecting = false;
        }
    }

    private setupEventListeners(): void {
        this.obs.on('ConnectionOpened', () => {
            this.log('WebSocket connection opened');
        });

        this.obs.on('ConnectionClosed', () => {
            this.log('WebSocket connection closed');
            this.updateConnectionStatus({ connected: false });
            this.scheduleReconnect();
        });

        this.obs.on('ConnectionError', (error) => {
            this.log('WebSocket connection error:', error.message);
            this.updateConnectionStatus({ connected: false, error: error.message });
        });

        // Écouter les changements d'état des sources audio
        this.obs.on('InputMuteStateChanged', (data) => {
            this.handleInputMuteChanged(data.inputName, data.inputMuted);
        });

        this.obs.on('InputVolumeChanged', (data) => {
            this.handleInputVolumeChanged(data.inputName, data.inputVolumeDb, data.inputVolumeMul);
        });

        // Écouter les changements de scène
        this.obs.on('CurrentProgramSceneChanged', (data) => {
            this.handleSceneChanged(data.sceneName);
        });

        // Écouter les changements de visibilité des sources
        this.obs.on('SceneItemEnableStateChanged', (data) => {
            this.handleSceneItemVisibilityChanged(data.sceneName, data.sceneItemId, data.sceneItemEnabled);
        });
    }

    private startPing(): void {
        this.pingTimer = setInterval(() => {
            if (this.obs.identified) {
                // Ping simple pour maintenir la connexion
                this.obs.call('GetVersion').catch(() => {
                    this.log('Ping failed - connection may be lost');
                });
            }
        }, this.config.timing.pingInterval);
    }

    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }

        this.reconnectTimer = setTimeout(() => {
            this.log('Attempting to reconnect...');
            this.connect();
        }, this.config.timing.reconnectInterval);
    }

    private async initializeSceneStates(): Promise<void> {
        try {
            // Obtenir la scène actuelle
            const currentSceneInfo = await this.obs.call('GetCurrentProgramScene') as any;
            this.currentScene = currentSceneInfo.sceneName as string;
            this.updateState('currentScene', this.currentScene);

            // Obtenir la liste de toutes les scènes
            const sceneListInfo = await this.obs.call('GetSceneList') as any;

            for (const scene of sceneListInfo.scenes) {
                const sceneName = scene.sceneName as string;

                // Initialiser l'état de la scène
                this.scenes[sceneName] = {
                    sceneName,
                    isCurrentScene: sceneName === this.currentScene,
                    sources: {},
                };

                // Obtenir les sources de cette scène
                try {
                    const sceneItemsInfo = await this.obs.call('GetSceneItemList', { sceneName }) as any;

                    for (const item of sceneItemsInfo.sceneItems) {
                        this.scenes[sceneName].sources[item.sourceName as string] = {
                            sceneItemId: item.sceneItemId as number,
                            sourceName: item.sourceName as string,
                            sceneItemEnabled: item.sceneItemEnabled as boolean,
                            sceneItemVisible: true, // OBS ne fournit pas toujours cette info
                        };
                    }
                } catch (error: any) {
                    this.log(`Warning: Could not get scene items for scene "${sceneName}":`, error.message);
                }
            }

            this.log(`Initialized ${Object.keys(this.scenes).length} scenes, current: ${this.currentScene}`);
        } catch (error: any) {
            this.handleError(error, 'Failed to initialize scene states');
        }
    }

    private handleInputMuteChanged(inputName: string, inputMuted: boolean): void {
        // Mettre à jour l'état local
        if (!this.audioSources[inputName]) {
            this.audioSources[inputName] = {
                inputName,
                inputVolumeMul: 1,
                inputVolumeDb: 0,
                inputMuted,
            };
        } else {
            this.audioSources[inputName].inputMuted = inputMuted;
        }

        this.updateState(`source_${inputName}_muted`, inputMuted);

        // Émettre un événement de changement de micro
        this.emitEvent(MODULE_EVENTS.MIC_STATE_CHANGED, {
            micName: inputName,
            active: !inputMuted, // Inverser : muted = false signifie actif
            timestamp: new Date(),
        });
    }

    private handleInputVolumeChanged(inputName: string, inputVolumeDb: number, inputVolumeMul: number): void {
        // Mettre à jour l'état local
        if (!this.audioSources[inputName]) {
            this.audioSources[inputName] = {
                inputName,
                inputVolumeMul,
                inputVolumeDb,
                inputMuted: false,
            };
        } else {
            this.audioSources[inputName].inputVolumeDb = inputVolumeDb;
            this.audioSources[inputName].inputVolumeMul = inputVolumeMul;
        }

        this.updateState(`source_${inputName}_volume`, { db: inputVolumeDb, mul: inputVolumeMul });

        // Émettre un événement de changement de volume
        this.emitEvent('volume_changed', {
            sourceName: inputName,
            volumeDb: inputVolumeDb,
            volumeMul: inputVolumeMul,
            timestamp: new Date(),
        });
    }

    private handleSceneChanged(sceneName: string): void {
        const previousScene = this.currentScene;
        this.currentScene = sceneName;

        this.updateState('currentScene', sceneName);

        // Émettre un événement de changement de scène
        this.emitEvent('scene_changed', {
            sceneName,
            previousScene,
            timestamp: new Date(),
        });

        this.log(`Scene changed: ${previousScene} → ${sceneName}`);
    }

    private async handleSceneItemVisibilityChanged(sceneName: string, sceneItemId: number, enabled: boolean): Promise<void> {
        try {
            // Obtenir les informations de la source pour ce scene item
            const sceneItemInfo = await this.obs.call('GetSceneItemSource', {
                sceneName,
                sceneItemId,
            }) as any;

            const sourceName = sceneItemInfo.sourceName as string;

            // Mettre à jour l'état local de la scène
            if (!this.scenes[sceneName]) {
                this.scenes[sceneName] = {
                    sceneName,
                    isCurrentScene: sceneName === this.currentScene,
                    sources: {},
                };
            }

            if (!this.scenes[sceneName].sources[sourceName]) {
                this.scenes[sceneName].sources[sourceName] = {
                    sceneItemId,
                    sourceName,
                    sceneItemEnabled: enabled,
                    sceneItemVisible: true, // Par défaut, on assume visible
                };
            } else {
                this.scenes[sceneName].sources[sourceName].sceneItemEnabled = enabled;
            }

            this.updateState(`scene_${sceneName}_source_${sourceName}_enabled`, enabled);

            // Émettre un événement si c'est la scène actuelle
            if (sceneName === this.currentScene) {
                this.emitEvent('source_visibility_changed', {
                    sourceName,
                    sceneName,
                    visible: enabled,
                    timestamp: new Date(),
                });

                this.log(`Source visibility changed in current scene: ${sourceName} = ${enabled}`);
            }
        } catch (error) {
            this.handleError(error, `Failed to handle scene item visibility change for scene ${sceneName}, item ${sceneItemId}`);
        }
    }
}
