/**
 * Module OBS autonome - Version nettoyée
 * Responsabilité : Connexion WebSocket et orchestration des services OBS
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { OBSWebSocket } from 'obs-websocket-js';
import { BaseModule } from '../core/base/base-module';
import { IMicrophoneModule } from '../core/interfaces/module.interface';
import { obsConfig, modulesConfig } from '../config';
import { OBSObserverService } from './obs-observer.service';
import { OBSControllerService } from './obs-controller.service';

@Injectable()
export class OBSModuleService extends BaseModule implements IMicrophoneModule, OnModuleInit, OnModuleDestroy {
    private obs: OBSWebSocket;
    private reconnectTimer?: NodeJS.Timeout;
    private pingTimer?: NodeJS.Timeout;
    private isConnecting = false;
    private readonly config = obsConfig;

    // Services spécialisés
    private observer: OBSObserverService;
    private controller: OBSControllerService;

    constructor() {
        super('obs', modulesConfig.obs.enabled);
        this.obs = new OBSWebSocket();
        this.observer = new OBSObserverService();
        this.controller = new OBSControllerService();
        this.setupServices();
        this.log('OBS module created', { enabled: this.enabled });
    }

    private setupServices(): void {
        // Configurer le callback pour les événements de l'observer
        this.observer.setEventCallback((type: string, data: any) => {
            // Émettre l'événement vers les autres modules
            this.emitEvent(type, data);
            
            // Mettre à jour l'état du module si nécessaire
            if (type === 'state_updated') {
                Object.entries(data as Record<string, any>).forEach(([key, value]) => {
                    this.updateState(key, value);
                });
            }
        });

        // Configurer les listeners de connexion OBS
        this.setupConnectionListeners();
    }

    async onModuleInit() {
        if (this.enabled) {
            await this.start();
        } else {
            this.log('Module disabled - not starting');
        }
    }

    async onModuleDestroy() {
        await this.stop();
    }

    async start(): Promise<void> {
        if (!this.enabled) {
            this.log('Cannot start - module is disabled');
            return;
        }

        if (this.config.autoConnect) {
            await this.connect();
        }
    }

    async stop(): Promise<void> {
        this.log('Stopping OBS module...');

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = undefined;
        }

        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = undefined;
        }

        if (this.obs.identified) {
            await this.obs.disconnect();
        }

        this.observer.reset();
        this.updateConnectionStatus({ connected: false });
        this.log('OBS module stopped');
    }

    // === INTERFACE IMicrophoneModule ===

    getMicState(sourceName: string): boolean | undefined {
        return this.observer.getMicState(sourceName);
    }

    getAllMicStates(): Record<string, boolean> {
        return this.observer.getAllMicStates();
    }

    // === MÉTHODES PUBLIQUES - OBSERVATION DES ÉTATS ===

    getCurrentScene(): string | null {
        return this.observer.getCurrentScene();
    }

    getAllScenes() {
        return this.observer.getAllScenes();
    }

    getSceneState(sceneName: string) {
        return this.observer.getSceneState(sceneName);
    }

    isSourceVisibleInCurrentScene(sourceName: string): boolean | undefined {
        return this.observer.isSourceVisibleInCurrentScene(sourceName);
    }

    getVisibleSourcesInCurrentScene(): string[] {
        return this.observer.getVisibleSourcesInCurrentScene();
    }

    getAllAudioLevels() {
        return this.observer.getAllAudioLevels();
    }

    // === MÉTHODES PUBLIQUES - CONTRÔLE OBS ===

    /**
     * Changer de scène OBS (exemple de contrôle)
     */
    async changeScene(sceneName: string): Promise<void> {
        if (!this.isReady()) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.changeScene(this.obs, sceneName);
        } catch (error: any) {
            this.handleError(error, `Failed to change scene to "${sceneName}"`);
            throw error;
        }
    }

    // === MÉTHODES PRIVÉES ===

    private setupConnectionListeners(): void {
        this.obs.on('ConnectionOpened', () => {
            this.log('WebSocket connection opened');
        });

        this.obs.on('ConnectionClosed', () => {
            this.log('WebSocket connection closed');
            this.updateConnectionStatus({ connected: false });
            this.observer.reset();
            this.scheduleReconnect();
        });

        this.obs.on('ConnectionError', (error) => {
            this.log('WebSocket connection error:', error.message);
            this.updateConnectionStatus({ connected: false, error: error.message });
        });
    }

    private async connect(): Promise<void> {
        if (this.isConnecting || this.obs.identified) {
            return;
        }

        this.isConnecting = true;

        try {
            this.log(`Connecting to OBS at ${this.config.network.host}:${this.config.network.port}...`);

            const url = `ws://${this.config.network.host}:${this.config.network.port}`;
            const connectionInfo = await this.obs.connect(url, this.config.network.password);

            this.log(`Connected to OBS WebSocket v${connectionInfo.obsWebSocketVersion} (RPC v${connectionInfo.negotiatedRpcVersion})`);

            this.updateConnectionStatus({ connected: true });
            this.startPing();
            
            // Configurer les listeners d'événements et initialiser les états
            this.observer.setupEventListeners(this.obs);
            await this.observer.initializeStates(this.obs);
        } catch (error: any) {
            this.handleError(error, 'Failed to connect to OBS');
            this.scheduleReconnect();
        } finally {
            this.isConnecting = false;
        }
    }

    private startPing(): void {
        this.pingTimer = setInterval(() => {
            if (this.obs.identified) {
                this.obs.call('GetVersion').catch(() => {
                    this.log('Ping failed - connection may be lost');
                });
            }
        }, this.config.timing.pingInterval);
    }

    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }

        this.reconnectTimer = setTimeout(() => {
            this.log('Attempting to reconnect...');
            this.connect();
        }, this.config.timing.reconnectInterval);
    }

    private isReady(): boolean {
        return this.obs.identified;
    }

    private handleError(error: any, context: string): void {
        const message = error?.message || 'Unknown error';
        this.log(`${context}: ${message}`);
        this.updateConnectionStatus({ connected: false, error: message });
    }
}
