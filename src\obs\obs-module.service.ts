/**
 * Module OBS autonome - Version nettoyée
 * Responsabilité : Connexion WebSocket et orchestration des services OBS
 */

import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { OBSWebSocket } from 'obs-websocket-js';
import { BaseModule } from '../core/base/base-module';
import { obsConfig, modulesConfig } from '../config';
import { OBSObserverService } from './obs-observer.service';
import { OBSControllerService } from './obs-controller.service';

@Injectable()
export class OBSModuleService extends BaseModule implements OnModuleInit, OnModuleDestroy {
    private obs: OBSWebSocket;
    private reconnectTimer?: NodeJS.Timeout;
    private pingTimer?: NodeJS.Timeout;
    private audioLevelTimer?: NodeJS.Timeout;
    private isConnecting = false;
    private readonly config = obsConfig;

    // Services spécialisés
    private observer: OBSObserverService;
    private controller: OBSControllerService;

    constructor() {
        super('obs', modulesConfig.obs.enabled);
        this.obs = new OBSWebSocket();
        this.observer = new OBSObserverService();
        this.controller = new OBSControllerService();
        this.setupServices();
        this.log('OBS module created', { enabled: this.enabled });
    }

    private setupServices(): void {
        // Configurer le callback pour les événements de l'observer
        this.observer.setEventCallback((type: string, data: any) => {
            // Émettre l'événement vers les autres modules
            this.emitEvent(type, data);
        });

        // Configurer les listeners de connexion OBS
        this.setupConnectionListeners();
    }

    async onModuleInit() {
        if (this.enabled) {
            await this.start();
        } else {
            this.log('Module disabled - not starting');
        }
    }

    async onModuleDestroy() {
        await this.stop();
    }

    async start(): Promise<void> {
        if (!this.enabled) {
            this.log('Cannot start - module is disabled');
            return;
        }

        if (this.config.autoConnect) {
            await this.connect();
        }
    }

    async stop(): Promise<void> {
        this.log('Stopping OBS module...');

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = undefined;
        }

        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = undefined;
        }

        if (this.audioLevelTimer) {
            clearInterval(this.audioLevelTimer);
            this.audioLevelTimer = undefined;
        }

        if (this.obs.identified) {
            await this.obs.disconnect();
        }

        this.updateConnectionStatus({ connected: false });
        this.log('OBS module stopped');
    }

    // === MÉTHODES PUBLIQUES - CONTRÔLE OBS ===

    /**
     * Changer de scène OBS (exemple de contrôle)
     */
    async changeScene(sceneName: string): Promise<void> {
        if (!this.obs.identified) {
            throw new Error('OBS not connected');
        }

        try {
            await this.controller.changeScene(this.obs, sceneName);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), `Failed to change scene to "${sceneName}"`);
            throw error;
        }
    }

    // === MÉTHODES PRIVÉES ===

    private setupConnectionListeners(): void {
        this.obs.on('ConnectionOpened', () => {
            this.log('WebSocket connection opened');
        });

        this.obs.on('ConnectionClosed', () => {
            this.log('WebSocket connection closed');
            this.updateConnectionStatus({ connected: false });
            this.scheduleReconnect();
        });

        this.obs.on('ConnectionError', (error) => {
            this.log('WebSocket connection error:', error.message);
            this.updateConnectionStatus({ connected: false, error: error.message });
        });
    }

    private async connect(): Promise<void> {
        if (this.isConnecting || this.obs.identified) {
            return;
        }

        this.isConnecting = true;

        try {
            this.log(`Connecting to OBS at ${this.config.network.host}:${this.config.network.port}...`);

            const url = `ws://${this.config.network.host}:${this.config.network.port}`;
            const connectionInfo = await this.obs.connect(url, this.config.network.password);

            this.log(`Connected to OBS WebSocket v${connectionInfo.obsWebSocketVersion} (RPC v${connectionInfo.negotiatedRpcVersion})`);

            this.updateConnectionStatus({ connected: true });
            this.startPing();
            this.startAudioLevelMonitoring();

            // Configurer les listeners d'événements
            this.observer.setupEventListeners(this.obs);
        } catch (error: any) {
            const errorMessage = String(error?.message || 'Unknown error');
            this.handleError(new Error(errorMessage), 'Failed to connect to OBS');
            this.scheduleReconnect();
        } finally {
            this.isConnecting = false;
        }
    }

    private startPing(): void {
        this.pingTimer = setInterval(() => {
            if (this.obs.identified) {
                this.obs.call('GetVersion').catch(() => {
                    this.log('Ping failed - connection may be lost');
                });
            }
        }, this.config.timing.pingInterval);
    }

    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
        }

        this.reconnectTimer = setTimeout(() => {
            this.log('Attempting to reconnect...');
            void this.connect();
        }, this.config.timing.reconnectInterval);
    }

    /**
     * Démarrer la surveillance des niveaux audio
     */
    private startAudioLevelMonitoring(): void {
        // Vérifier les niveaux audio toutes les 100ms pour une détection réactive
        this.audioLevelTimer = setInterval(() => {
            void this.checkAudioLevels();
        }, 100);
    }

    /**
     * Vérifier les niveaux audio des sources et détecter l'activité
     */
    private async checkAudioLevels(): Promise<void> {
        if (!this.obs.identified) {
            return;
        }

        try {
            // Obtenir les niveaux audio de toutes les sources
            const response = await this.obs.call('GetInputList');

            for (const input of response.inputs) {
                // Vérifier seulement les sources audio qui nous intéressent
                const inputName = String(input.inputName || '');
                if (inputName === 'Capture audio (entrée)' || inputName.includes('MIC') || inputName.includes('Invite')) {
                    await this.checkSourceAudioLevel(inputName);
                }
            }
        } catch (error: any) {
            // Erreur silencieuse pour éviter le spam de logs
        }
    }

    /**
     * Vérifier le niveau audio d'une source spécifique
     */
    private async checkSourceAudioLevel(sourceName: string): Promise<void> {
        try {
            const response = await this.obs.call('GetInputVolume', {
                inputName: sourceName
            });

            // Détecter l'activité audio basée sur le volume
            // Si le volume n'est pas à -100dB (mute), on considère qu'il y a potentiellement de l'audio
            const isActive = response.inputVolumeDb > -90; // Seuil de détection

            this.emitEvent('audio_activity_detected', {
                sourceName,
                isActive,
                volumeDb: response.inputVolumeDb,
                volumeMul: response.inputVolumeMul,
            });
        } catch (error: any) {
            // Source peut ne pas exister ou ne pas être audio
        }
    }
}
