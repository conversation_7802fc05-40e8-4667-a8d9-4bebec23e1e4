/**
 * Service d'observation des événements OBS - Version ultra-simplifiée
 * Responsabilité : Écouter OBS, émettre des événements
 */

import { Injectable } from '@nestjs/common';
import { OBSWebSocket } from 'obs-websocket-js';

export type OBSEventCallback = (type: string, data: any) => void;

@Injectable()
export class OBSObserverService {
    private eventCallback?: OBSEventCallback;

    constructor() {}

    setEventCallback(callback: OBSEventCallback): void {
        this.eventCallback = callback;
    }

    setupEventListeners(obs: OBSWebSocket): void {
        obs.on('InputMuteStateChanged', (data) => {
            this.handleInputMuteChanged(data.inputName, data.inputMuted);
        });

        obs.on('InputVolumeChanged', (data) => {
            this.handleInputVolumeChanged(data.inputName, data.inputVolumeDb, data.inputVolumeMul);
        });

        obs.on('CurrentProgramSceneChanged', (data) => {
            this.handleSceneChanged(data.sceneName);
        });

        // Écouter les changements de visibilité des sources (pour détecter l'activité audio)
        obs.on('SceneItemEnableStateChanged', (data) => {
            this.handleSourceVisibilityChanged(data.sceneName, data.sceneItemId, data.sceneItemEnabled);
        });
    }

    // === HANDLERS SIMPLES ===

    private handleInputMuteChanged(inputName: string, inputMuted: boolean): void {
        this.emitEvent('input_mute_changed', {
            inputName,
            inputMuted,
        });
    }

    private handleInputVolumeChanged(inputName: string, inputVolumeDb: number, inputVolumeMul: number): void {
        this.emitEvent('input_volume_changed', {
            inputName,
            inputVolumeDb,
            inputVolumeMul,
        });
    }

    private handleSceneChanged(sceneName: string): void {
        this.emitEvent('scene_changed', {
            sceneName,
        });
    }

    private handleSourceVisibilityChanged(sceneName: string, sceneItemId: number, enabled: boolean): void {
        this.emitEvent('source_visibility_changed', {
            sceneName,
            sceneItemId,
            enabled,
        });
    }

    private emitEvent(type: string, data: any): void {
        if (this.eventCallback) {
            this.eventCallback(type, data);
        }
    }
}
