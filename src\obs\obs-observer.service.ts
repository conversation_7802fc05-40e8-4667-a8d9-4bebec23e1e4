/**
 * Service d'observation des événements OBS
 * Responsabilité : Écouter et traiter les événements OBS en temps réel
 */

import { Injectable } from '@nestjs/common';
import { OBSWebSocket } from 'obs-websocket-js';
import { OBSAudioSource, OBSSceneState, OBSSceneItem } from './obs.types';
import { MODULE_EVENTS } from '../core/interfaces/module.interface';

export type OBSEventCallback = (type: string, data: any) => void;

@Injectable()
export class OBSObserverService {
    private eventCallback?: OBSEventCallback;

    // États observés
    private audioSources: Record<string, OBSAudioSource> = {};
    private scenes: Record<string, OBSSceneState> = {};
    private currentScene: string | null = null;

    constructor() {}

    /**
     * Configurer le callback pour les événements
     */
    setEventCallback(callback: OBSEventCallback): void {
        this.eventCallback = callback;
    }

    /**
     * Configurer les listeners sur l'instance OBS WebSocket
     */
    setupEventListeners(obs: OBSWebSocket): void {
        // Écouter les changements d'état des sources audio
        obs.on('InputMuteStateChanged', (data) => {
            this.handleInputMuteChanged(data.inputName, data.inputMuted);
        });

        obs.on('InputVolumeChanged', (data) => {
            this.handleInputVolumeChanged(data.inputName, data.inputVolumeDb, data.inputVolumeMul);
        });

        // Écouter les changements de scène
        obs.on('CurrentProgramSceneChanged', (data) => {
            this.handleSceneChanged(data.sceneName);
        });

        // Écouter les changements de visibilité des sources
        obs.on('SceneItemEnableStateChanged', (data) => {
            this.handleSceneItemVisibilityChanged(obs, data.sceneName, data.sceneItemId, data.sceneItemEnabled);
        });
    }

    /**
     * Initialiser l'état des scènes au démarrage
     */
    async initializeStates(obs: OBSWebSocket): Promise<void> {
        try {
            // Obtenir la scène actuelle
            const currentSceneInfo = await obs.call('GetCurrentProgramScene') as any;
            this.currentScene = currentSceneInfo.sceneName as string;
            this.emitEvent('state_updated', { currentScene: this.currentScene });

            // Obtenir la liste de toutes les scènes
            const sceneListInfo = await obs.call('GetSceneList') as any;
            
            for (const scene of sceneListInfo.scenes) {
                const sceneName = scene.sceneName as string;
                
                // Initialiser l'état de la scène
                this.scenes[sceneName] = {
                    sceneName,
                    isCurrentScene: sceneName === this.currentScene,
                    sources: {},
                };

                // Obtenir les sources de cette scène
                try {
                    const sceneItemsInfo = await obs.call('GetSceneItemList', { sceneName }) as any;
                    
                    for (const item of sceneItemsInfo.sceneItems) {
                        this.scenes[sceneName].sources[item.sourceName as string] = {
                            sceneItemId: item.sceneItemId as number,
                            sourceName: item.sourceName as string,
                            sceneItemEnabled: item.sceneItemEnabled as boolean,
                            sceneItemVisible: true,
                        };
                    }
                } catch (error: any) {
                    console.log(`[obs-observer] Warning: Could not get scene items for scene "${sceneName}":`, error.message);
                }
            }

            console.log(`[obs-observer] Initialized ${Object.keys(this.scenes).length} scenes, current: ${this.currentScene}`);
        } catch (error: any) {
            console.error('[obs-observer] Failed to initialize scene states:', error.message);
        }
    }

    // === GETTERS POUR LES ÉTATS ===

    getCurrentScene(): string | null {
        return this.currentScene;
    }

    getAllScenes(): Record<string, OBSSceneState> {
        return { ...this.scenes };
    }

    getSceneState(sceneName: string): OBSSceneState | undefined {
        return this.scenes[sceneName];
    }

    getAllAudioLevels(): Record<string, { db: number; mul: number; muted: boolean }> {
        const levels: Record<string, { db: number; mul: number; muted: boolean }> = {};
        
        for (const [name, source] of Object.entries(this.audioSources)) {
            levels[name] = {
                db: source.inputVolumeDb,
                mul: source.inputVolumeMul,
                muted: source.inputMuted,
            };
        }
        
        return levels;
    }

    getMicState(sourceName: string): boolean | undefined {
        const source = this.audioSources[sourceName];
        return source ? !source.inputMuted : undefined;
    }

    getAllMicStates(): Record<string, boolean> {
        const states: Record<string, boolean> = {};
        for (const [name, source] of Object.entries(this.audioSources)) {
            states[name] = !source.inputMuted;
        }
        return states;
    }

    isSourceVisibleInCurrentScene(sourceName: string): boolean | undefined {
        if (!this.currentScene) return undefined;
        
        const currentSceneState = this.scenes[this.currentScene];
        if (!currentSceneState) return undefined;
        
        const source = currentSceneState.sources[sourceName];
        return source ? source.sceneItemVisible && source.sceneItemEnabled : undefined;
    }

    getVisibleSourcesInCurrentScene(): string[] {
        if (!this.currentScene) return [];
        
        const currentSceneState = this.scenes[this.currentScene];
        if (!currentSceneState) return [];
        
        return Object.entries(currentSceneState.sources)
            .filter(([_, source]) => source.sceneItemVisible && source.sceneItemEnabled)
            .map(([sourceName, _]) => sourceName);
    }

    // === HANDLERS PRIVÉS ===

    private handleInputMuteChanged(inputName: string, inputMuted: boolean): void {
        // Mettre à jour l'état local
        if (!this.audioSources[inputName]) {
            this.audioSources[inputName] = {
                inputName,
                inputVolumeMul: 1,
                inputVolumeDb: 0,
                inputMuted,
            };
        } else {
            this.audioSources[inputName].inputMuted = inputMuted;
        }

        // Émettre les événements
        this.emitEvent(MODULE_EVENTS.MIC_STATE_CHANGED, {
            micName: inputName,
            active: !inputMuted,
            timestamp: new Date(),
        });

        this.emitEvent('state_updated', { [`source_${inputName}_muted`]: inputMuted });
    }

    private handleInputVolumeChanged(inputName: string, inputVolumeDb: number, inputVolumeMul: number): void {
        // Mettre à jour l'état local
        if (!this.audioSources[inputName]) {
            this.audioSources[inputName] = {
                inputName,
                inputVolumeMul,
                inputVolumeDb,
                inputMuted: false,
            };
        } else {
            this.audioSources[inputName].inputVolumeDb = inputVolumeDb;
            this.audioSources[inputName].inputVolumeMul = inputVolumeMul;
        }

        // Émettre les événements
        this.emitEvent('volume_changed', {
            sourceName: inputName,
            volumeDb: inputVolumeDb,
            volumeMul: inputVolumeMul,
            timestamp: new Date(),
        });

        this.emitEvent('state_updated', { [`source_${inputName}_volume`]: { db: inputVolumeDb, mul: inputVolumeMul } });
    }

    private handleSceneChanged(sceneName: string): void {
        const previousScene = this.currentScene;
        this.currentScene = sceneName;
        
        // Mettre à jour les états des scènes
        Object.values(this.scenes).forEach(scene => {
            scene.isCurrentScene = scene.sceneName === sceneName;
        });
        
        // Émettre les événements
        this.emitEvent('scene_changed', {
            sceneName,
            previousScene,
            timestamp: new Date(),
        });

        this.emitEvent('state_updated', { currentScene: sceneName });

        console.log(`[obs-observer] Scene changed: ${previousScene} → ${sceneName}`);
    }

    private async handleSceneItemVisibilityChanged(obs: OBSWebSocket, sceneName: string, sceneItemId: number, enabled: boolean): Promise<void> {
        try {
            // Obtenir les informations de la source pour ce scene item
            const sceneItemInfo = await obs.call('GetSceneItemSource', {
                sceneName,
                sceneItemId,
            }) as any;

            const sourceName = sceneItemInfo.sourceName as string;

            // Mettre à jour l'état local de la scène
            if (!this.scenes[sceneName]) {
                this.scenes[sceneName] = {
                    sceneName,
                    isCurrentScene: sceneName === this.currentScene,
                    sources: {},
                };
            }

            if (!this.scenes[sceneName].sources[sourceName]) {
                this.scenes[sceneName].sources[sourceName] = {
                    sceneItemId,
                    sourceName,
                    sceneItemEnabled: enabled,
                    sceneItemVisible: true,
                };
            } else {
                this.scenes[sceneName].sources[sourceName].sceneItemEnabled = enabled;
            }

            // Émettre un événement si c'est la scène actuelle
            if (sceneName === this.currentScene) {
                this.emitEvent('source_visibility_changed', {
                    sourceName,
                    sceneName,
                    visible: enabled,
                    timestamp: new Date(),
                });

                console.log(`[obs-observer] Source visibility changed in current scene: ${sourceName} = ${enabled}`);
            }

            this.emitEvent('state_updated', { [`scene_${sceneName}_source_${sourceName}_enabled`]: enabled });
        } catch (error: any) {
            console.error(`[obs-observer] Failed to handle scene item visibility change for scene ${sceneName}, item ${sceneItemId}:`, error.message);
        }
    }

    private emitEvent(type: string, data: any): void {
        if (this.eventCallback) {
            this.eventCallback(type, data);
        }
    }

    /**
     * Nettoyer les états (appelé lors de la déconnexion)
     */
    reset(): void {
        this.audioSources = {};
        this.scenes = {};
        this.currentScene = null;
    }
}
