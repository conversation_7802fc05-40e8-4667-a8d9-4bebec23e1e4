/**
 * Exemple de lien pour écouter les variables OBS
 * Réagit aux changements de la variable audio_active_invite1
 */

import { Injectable } from '@nestjs/common';
import { IModuleLink } from '../interfaces/module-link.interface';
import { OBSModuleService } from '../../obs/obs-module.service';

@Injectable()
export class OBSVariableListenerLink implements IModuleLink {
    constructor(private obsModule: OBSModuleService) {}

    async initialize(): Promise<void> {
        if (!this.obsModule.enabled) {
            console.log('[obs-variable-listener] OBS module disabled - link not active');
            return;
        }

        this.setupOBSListeners();
        console.log('[obs-variable-listener] Link initialized - listening for variable changes');
    }

    private setupOBSListeners(): void {
        this.obsModule.onEvent((type, data) => {
            if (type === 'variable_changed' && data.variableName === 'audio_active_invite1') {
                this.handleAudioActiveChange(data.variableValue, data.previousValue);
            }
        });
    }

    private handleAudioActiveChange(currentValue: any, previousValue: any): void {
        const isActive = currentValue === 1 || currentValue === '1' || currentValue === true;
        const wasActive = previousValue === 1 || previousValue === '1' || previousValue === true;

        console.log(`[obs-variable-listener] Audio invite 1 state changed: ${wasActive} → ${isActive}`);

        if (isActive && !wasActive) {
            console.log('🎤 Audio invite 1 ACTIVÉ - Déclenchement des automations...');
            this.onAudioInviteActivated();
        } else if (!isActive && wasActive) {
            console.log('🔇 Audio invite 1 DÉSACTIVÉ - Arrêt des automations...');
            this.onAudioInviteDeactivated();
        }
    }

    private onAudioInviteActivated(): void {
        // Ici vous pouvez ajouter vos automations quand l'audio devient actif
        // Exemples :
        // - Changer de scène OBS
        // - Envoyer une commande à Companion
        // - Ajuster des volumes
        // - Déclencher des presets de caméra
        
        console.log('🚀 Automations à déclencher :');
        console.log('   - Changer vers scène "Invite Active"');
        console.log('   - Activer preset caméra invité');
        console.log('   - Ajuster mix audio');
    }

    private onAudioInviteDeactivated(): void {
        // Ici vous pouvez ajouter vos automations quand l'audio devient inactif
        console.log('🛑 Automations à arrêter :');
        console.log('   - Retour scène principale');
        console.log('   - Désactiver preset caméra invité');
    }
}
