/**
 * Configuration par défaut des modules
 *
 * ⚠️  NE PAS MODIFIER CE FICHIER
 *
 * Ce fichier contient la configuration par défaut des modules du Hub.
 * Pour personnaliser la configuration, créez un fichier `modules.local.ts`
 * dans le dossier `src/config/local/` avec vos paramètres spécifiques.
 */

/**
 * Configuration des modules activés/désactivés
 */
export interface ModulesConfig {
    gabin: {
        enabled: boolean;
        description: string;
    };
    obs: {
        enabled: boolean;
        description: string;
    };
    companion: {
        enabled: boolean;
        description: string;
    };
}

export const modulesDefaultConfig: ModulesConfig = {
    gabin: {
        enabled: false,
        description: 'Module Gabin pour caméras automatiques et gestion des micros',
    },
    obs: {
        enabled: true,
        description: 'Module OBS Studio pour contrôle via WebSocket',
    },
    companion: {
        enabled: true,
        description: 'Module Companion pour surfaces de contrôle OSC',
    },
};
